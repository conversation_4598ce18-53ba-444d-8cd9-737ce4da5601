"""
东方财富数据爬虫模块
"""
import requests
import json
import re
import time
import logging
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from fake_useragent import UserAgent
from retrying import retry
from config import HEADERS, EASTMONEY_BASE_URL

logger = logging.getLogger(__name__)

class EastMoneySpider:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(HEADERS)
        self.ua = UserAgent()
        
    def _get_random_headers(self):
        """获取随机请求头"""
        headers = HEADERS.copy()
        headers['User-Agent'] = self.ua.random
        return headers
    
    @retry(stop_max_attempt_number=3, wait_fixed=2000)
    def _make_request(self, url, method='GET', **kwargs):
        """发送HTTP请求，带重试机制"""
        try:
            headers = self._get_random_headers()
            if method.upper() == 'GET':
                response = self.session.get(url, headers=headers, timeout=10, **kwargs)
            else:
                response = self.session.post(url, headers=headers, timeout=10, **kwargs)
            
            response.raise_for_status()
            return response
        except Exception as e:
            logger.error(f"请求失败 {url}: {e}")
            raise
    
    def scrape_with_selenium(self, url, wait_element=None):
        """使用Selenium爬取动态内容"""
        driver = None
        try:
            # 配置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument(f'--user-agent={self.ua.random}')
            
            # 创建WebDriver
            driver = webdriver.Chrome(
                service=webdriver.chrome.service.Service(ChromeDriverManager().install()),
                options=chrome_options
            )
            
            driver.get(url)
            
            # 等待页面加载
            if wait_element:
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, wait_element))
                )
            else:
                time.sleep(3)
            
            # 获取页面源码
            page_source = driver.page_source
            return page_source
            
        except Exception as e:
            logger.error(f"Selenium爬取失败 {url}: {e}")
            return None
        finally:
            if driver:
                driver.quit()
    
    def parse_ranking_data(self, html_content):
        """解析排行榜数据"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            ranking_data = []
            
            # 查找排行榜容器
            ranking_containers = soup.find_all(['div', 'table', 'ul'], 
                                             class_=re.compile(r'rank|list|table', re.I))
            
            for container in ranking_containers:
                # 查找股票信息行
                rows = container.find_all(['tr', 'li', 'div'], 
                                        class_=re.compile(r'item|row|stock', re.I))
                
                for i, row in enumerate(rows[:100], 1):  # 限制前100条
                    try:
                        # 提取股票信息
                        stock_info = self._extract_stock_info(row, i)
                        if stock_info:
                            ranking_data.append(stock_info)
                    except Exception as e:
                        logger.warning(f"解析第{i}行数据失败: {e}")
                        continue
            
            return ranking_data
            
        except Exception as e:
            logger.error(f"解析排行榜数据失败: {e}")
            return []
    
    def _extract_stock_info(self, row_element, rank_num):
        """从行元素中提取股票信息"""
        try:
            text_content = row_element.get_text(strip=True)
            
            # 使用正则表达式提取信息
            # 股票代码模式
            code_pattern = r'(\d{6})'
            # 价格模式
            price_pattern = r'(\d+\.?\d*)'
            # 涨跌幅模式
            change_pattern = r'([+-]?\d+\.?\d*%)'
            
            stock_code_match = re.search(code_pattern, text_content)
            price_matches = re.findall(price_pattern, text_content)
            change_match = re.search(change_pattern, text_content)
            
            if stock_code_match:
                stock_code = stock_code_match.group(1)
                
                # 提取股票名称（通常在代码前面）
                stock_name = self._extract_stock_name(text_content, stock_code)
                
                # 提取价格（通常是第一个数字）
                current_price = float(price_matches[0]) if price_matches else 0.0
                
                # 提取涨跌幅
                change_percent = change_match.group(1) if change_match else "0%"
                
                return {
                    'rank_num': rank_num,
                    'stock_name': stock_name,
                    'stock_code': stock_code,
                    'sector': '科技板块',  # 默认板块
                    'current_price': current_price,
                    'change_percent': change_percent
                }
            
        except Exception as e:
            logger.warning(f"提取股票信息失败: {e}")
        
        return None
    
    def _extract_stock_name(self, text, stock_code):
        """从文本中提取股票名称"""
        try:
            # 查找股票代码前的文本作为股票名称
            parts = text.split(stock_code)
            if len(parts) > 1:
                name_part = parts[0].strip()
                # 清理名称，移除数字和特殊字符
                name = re.sub(r'^\d+\.?\s*', '', name_part)  # 移除序号
                name = re.sub(r'[^\u4e00-\u9fa5a-zA-Z]', '', name)  # 只保留中英文
                return name[:10] if name else f"股票{stock_code}"
        except:
            pass
        
        return f"股票{stock_code}"
    
    def get_popularity_ranking(self):
        """获取人气榜数据"""
        try:
            logger.info("开始获取人气榜数据...")
            
            # 方法1：尝试直接API请求
            api_data = self._try_api_request('popularity')
            if api_data:
                return api_data
            
            # 方法2：使用Selenium爬取页面
            url = "https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/topic"
            html_content = self.scrape_with_selenium(url, '.ranking-list')
            
            if html_content:
                return self.parse_ranking_data(html_content)
            
            logger.warning("所有获取人气榜数据的方法都失败了")
            return []
            
        except Exception as e:
            logger.error(f"获取人气榜数据失败: {e}")
            return []
    
    def get_soaring_ranking(self):
        """获取飙升榜数据"""
        try:
            logger.info("开始获取飙升榜数据...")
            
            # 方法1：尝试直接API请求
            api_data = self._try_api_request('soaring')
            if api_data:
                return api_data
            
            # 方法2：使用Selenium爬取页面
            url = "https://vipmoney.eastmoney.com/collect/app_ranking/ranking/app.html#/soaring"
            html_content = self.scrape_with_selenium(url, '.ranking-list')
            
            if html_content:
                return self.parse_ranking_data(html_content)
            
            logger.warning("所有获取飙升榜数据的方法都失败了")
            return []
            
        except Exception as e:
            logger.error(f"获取飙升榜数据失败: {e}")
            return []
    
    def _try_api_request(self, ranking_type):
        """尝试API请求获取数据"""
        try:
            # 这里需要根据实际的API接口进行调整
            api_urls = {
                'popularity': f"{EASTMONEY_BASE_URL}/api/ranking/popularity",
                'soaring': f"{EASTMONEY_BASE_URL}/api/ranking/soaring"
            }
            
            if ranking_type in api_urls:
                response = self._make_request(api_urls[ranking_type])
                if response.status_code == 200:
                    data = response.json()
                    return self._parse_api_data(data)
            
        except Exception as e:
            logger.debug(f"API请求失败: {e}")
        
        return None
    
    def _parse_api_data(self, api_data):
        """解析API返回的数据"""
        try:
            # 根据实际API响应格式进行解析
            if isinstance(api_data, dict) and 'data' in api_data:
                items = api_data['data']
                ranking_data = []
                
                for i, item in enumerate(items[:100], 1):
                    stock_info = {
                        'rank_num': i,
                        'stock_name': item.get('name', ''),
                        'stock_code': item.get('code', ''),
                        'sector': item.get('sector', '科技板块'),
                        'current_price': float(item.get('price', 0)),
                        'change_percent': item.get('change', '0%')
                    }
                    ranking_data.append(stock_info)
                
                return ranking_data
        except Exception as e:
            logger.error(f"解析API数据失败: {e}")
        
        return None
