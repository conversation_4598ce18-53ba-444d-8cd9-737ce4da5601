"""
Web API服务
"""
from flask import Flask, jsonify, request, render_template_string
from flask_cors import CORS
import logging
from datetime import datetime
from database import DatabaseManager
from scheduler import scheduler_instance, start_scheduler, stop_scheduler, get_scheduler_status
from config import WEB_HOST, WEB_PORT, DEBUG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 初始化数据库管理器
db_manager = DatabaseManager()

@app.route('/')
def index():
    """主页"""
    return render_template_string('''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>东方财富数据监控系统</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; }
            .header { text-align: center; margin-bottom: 30px; }
            .card { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .status { display: flex; justify-content: space-between; align-items: center; }
            .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
            .btn-primary { background-color: #007bff; color: white; }
            .btn-success { background-color: #28a745; color: white; }
            .btn-danger { background-color: #dc3545; color: white; }
            .api-list { list-style: none; padding: 0; }
            .api-list li { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; }
            .api-list a { text-decoration: none; color: #007bff; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>东方财富数据监控系统</h1>
                <p>实时监控人气榜和飙升榜数据</p>
            </div>
            
            <div class="card">
                <h2>系统状态</h2>
                <div class="status">
                    <div>
                        <span id="status-text">检查中...</span>
                        <span id="last-update"></span>
                    </div>
                    <div>
                        <button class="btn btn-success" onclick="startScheduler()">启动调度器</button>
                        <button class="btn btn-danger" onclick="stopScheduler()">停止调度器</button>
                        <button class="btn btn-primary" onclick="updateData()">立即更新</button>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h2>API接口</h2>
                <ul class="api-list">
                    <li><a href="/api/popularity" target="_blank">GET /api/popularity</a> - 获取人气榜数据</li>
                    <li><a href="/api/soaring" target="_blank">GET /api/soaring</a> - 获取飙升榜数据</li>
                    <li><a href="/api/status" target="_blank">GET /api/status</a> - 获取系统状态</li>
                    <li><a href="/dashboard" target="_blank">GET /dashboard</a> - 数据看板</li>
                </ul>
            </div>
        </div>
        
        <script>
            function checkStatus() {
                fetch('/api/status')
                    .then(response => response.json())
                    .then(data => {
                        const statusText = document.getElementById('status-text');
                        const lastUpdate = document.getElementById('last-update');
                        
                        if (data.scheduler.is_running) {
                            statusText.textContent = '运行中';
                            statusText.style.color = 'green';
                        } else {
                            statusText.textContent = '已停止';
                            statusText.style.color = 'red';
                        }
                        
                        if (data.last_update) {
                            lastUpdate.textContent = `最后更新: ${data.last_update}`;
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('status-text').textContent = '检查失败';
                    });
            }
            
            function startScheduler() {
                fetch('/api/scheduler/start', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                        checkStatus();
                    });
            }
            
            function stopScheduler() {
                fetch('/api/scheduler/stop', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                        checkStatus();
                    });
            }
            
            function updateData() {
                fetch('/api/update', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message);
                        checkStatus();
                    });
            }
            
            // 页面加载时检查状态
            checkStatus();
            
            // 每30秒检查一次状态
            setInterval(checkStatus, 30000);
        </script>
    </body>
    </html>
    ''')

@app.route('/api/popularity')
def get_popularity_data():
    """获取人气榜数据"""
    try:
        limit = request.args.get('limit', 100, type=int)
        data = db_manager.get_latest_popularity_data(limit)
        
        return jsonify({
            'success': True,
            'data': data,
            'count': len(data),
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/soaring')
def get_soaring_data():
    """获取飙升榜数据"""
    try:
        limit = request.args.get('limit', 100, type=int)
        data = db_manager.get_latest_soaring_data(limit)
        
        return jsonify({
            'success': True,
            'data': data,
            'count': len(data),
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/status')
def get_status():
    """获取系统状态"""
    try:
        scheduler_status = get_scheduler_status()
        update_status = db_manager.get_update_status()
        
        last_update = None
        if update_status:
            last_update = max([status['last_update'] for status in update_status])
        
        return jsonify({
            'success': True,
            'scheduler': scheduler_status,
            'update_status': update_status,
            'last_update': last_update,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/scheduler/start', methods=['POST'])
def start_scheduler_api():
    """启动调度器"""
    try:
        start_scheduler()
        return jsonify({
            'success': True,
            'message': '调度器启动成功',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'调度器启动失败: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/scheduler/stop', methods=['POST'])
def stop_scheduler_api():
    """停止调度器"""
    try:
        stop_scheduler()
        return jsonify({
            'success': True,
            'message': '调度器停止成功',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'调度器停止失败: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/update', methods=['POST'])
def manual_update():
    """手动更新数据"""
    try:
        # 手动触发数据更新
        scheduler_instance.update_all_data()
        
        return jsonify({
            'success': True,
            'message': '数据更新完成',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'数据更新失败: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/dashboard')
def dashboard():
    """数据看板"""
    return render_template_string('''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>数据看板 - 东方财富监控</title>
        <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            .container { max-width: 1400px; margin: 0 auto; }
            .header { text-align: center; margin-bottom: 30px; }
            .dashboard { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
            .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .table-container { max-height: 600px; overflow-y: auto; }
            table { width: 100%; border-collapse: collapse; }
            th, td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #ddd; }
            th { background-color: #f8f9fa; font-weight: bold; }
            .positive { color: #28a745; }
            .negative { color: #dc3545; }
            .refresh-btn { padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>东方财富数据看板</h1>
                <button class="refresh-btn" onclick="loadData()">刷新数据</button>
            </div>
            
            <div class="dashboard">
                <div class="card">
                    <h2>人气榜 TOP 50</h2>
                    <div class="table-container">
                        <table id="popularity-table">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>股票名称</th>
                                    <th>股票代码</th>
                                    <th>当前价格</th>
                                    <th>涨跌幅</th>
                                </tr>
                            </thead>
                            <tbody id="popularity-tbody">
                                <tr><td colspan="5">加载中...</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="card">
                    <h2>飙升榜 TOP 50</h2>
                    <div class="table-container">
                        <table id="soaring-table">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>股票名称</th>
                                    <th>股票代码</th>
                                    <th>当前价格</th>
                                    <th>涨跌幅</th>
                                </tr>
                            </thead>
                            <tbody id="soaring-tbody">
                                <tr><td colspan="5">加载中...</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            function loadData() {
                loadPopularityData();
                loadSoaringData();
            }
            
            function loadPopularityData() {
                fetch('/api/popularity?limit=50')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            updateTable('popularity-tbody', data.data);
                        } else {
                            document.getElementById('popularity-tbody').innerHTML = '<tr><td colspan="5">加载失败</td></tr>';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('popularity-tbody').innerHTML = '<tr><td colspan="5">加载失败</td></tr>';
                    });
            }
            
            function loadSoaringData() {
                fetch('/api/soaring?limit=50')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            updateTable('soaring-tbody', data.data);
                        } else {
                            document.getElementById('soaring-tbody').innerHTML = '<tr><td colspan="5">加载失败</td></tr>';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('soaring-tbody').innerHTML = '<tr><td colspan="5">加载失败</td></tr>';
                    });
            }
            
            function updateTable(tbodyId, data) {
                const tbody = document.getElementById(tbodyId);
                if (data.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="5">暂无数据</td></tr>';
                    return;
                }
                
                tbody.innerHTML = data.map(item => {
                    const changeClass = item.change_percent.startsWith('+') ? 'positive' : 
                                       item.change_percent.startsWith('-') ? 'negative' : '';
                    
                    return `
                        <tr>
                            <td>${item.rank_num}</td>
                            <td>${item.stock_name}</td>
                            <td>${item.stock_code}</td>
                            <td>${item.current_price}</td>
                            <td class="${changeClass}">${item.change_percent}</td>
                        </tr>
                    `;
                }).join('');
            }
            
            // 页面加载时获取数据
            loadData();
            
            // 每分钟自动刷新数据
            setInterval(loadData, 60000);
        </script>
    </body>
    </html>
    ''')

if __name__ == '__main__':
    # 启动调度器
    start_scheduler()
    
    # 启动Web服务
    app.run(host=WEB_HOST, port=WEB_PORT, debug=DEBUG)
