"""
排行榜表格组件
"""
from PyQt5.QtWidgets import (QTableWidget, QTableWidgetItem, QHeaderView, 
                             QAbstractItemView, QMenu, QAction, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QColor, QFont, QCursor

from core.config import TABLE_COLUMNS, COLORS

class RankingTableWidget(QTableWidget):
    """排行榜表格组件"""
    
    # 信号定义
    stock_selected = pyqtSignal(dict)  # 股票被选中
    
    def __init__(self, table_type='popularity'):
        super().__init__()
        self.table_type = table_type
        self.stock_data = []
        self.setup_table()
        self.setup_context_menu()
    
    def setup_table(self):
        """设置表格"""
        # 获取列配置
        columns = TABLE_COLUMNS[self.table_type]
        
        # 设置行列数
        self.setColumnCount(len(columns))
        self.setRowCount(0)
        
        # 设置表头
        headers = [col[0] for col in columns]
        self.setHorizontalHeaderLabels(headers)
        
        # 设置列宽
        for i, (_, width) in enumerate(columns):
            self.setColumnWidth(i, width)
        
        # 设置表格属性
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.setSelectionMode(QAbstractItemView.SingleSelection)
        self.setSortingEnabled(True)
        self.setShowGrid(True)
        
        # 设置表头
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        # 设置垂直表头
        self.verticalHeader().setVisible(False)
        
        # 连接信号
        self.itemDoubleClicked.connect(self.on_item_double_clicked)
    
    def setup_context_menu(self):
        """设置右键菜单"""
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.itemAt(position) is None:
            return
        
        menu = QMenu(self)
        
        # 查看详情
        view_action = QAction("查看详情", self)
        view_action.triggered.connect(self.view_stock_detail)
        menu.addAction(view_action)
        
        # 复制股票代码
        copy_code_action = QAction("复制股票代码", self)
        copy_code_action.triggered.connect(self.copy_stock_code)
        menu.addAction(copy_code_action)
        
        # 复制股票名称
        copy_name_action = QAction("复制股票名称", self)
        copy_name_action.triggered.connect(self.copy_stock_name)
        menu.addAction(copy_name_action)
        
        menu.exec_(self.mapToGlobal(position))
    
    def view_stock_detail(self):
        """查看股票详情"""
        current_row = self.currentRow()
        if current_row >= 0 and current_row < len(self.stock_data):
            stock = self.stock_data[current_row]
            self.stock_selected.emit(stock)
    
    def copy_stock_code(self):
        """复制股票代码"""
        current_row = self.currentRow()
        if current_row >= 0 and current_row < len(self.stock_data):
            stock = self.stock_data[current_row]
            from PyQt5.QtWidgets import QApplication
            QApplication.clipboard().setText(stock.get('code', ''))
    
    def copy_stock_name(self):
        """复制股票名称"""
        current_row = self.currentRow()
        if current_row >= 0 and current_row < len(self.stock_data):
            stock = self.stock_data[current_row]
            from PyQt5.QtWidgets import QApplication
            QApplication.clipboard().setText(stock.get('name', ''))
    
    def on_item_double_clicked(self, item):
        """双击事件"""
        self.view_stock_detail()
    
    def update_data(self, data):
        """更新表格数据"""
        self.stock_data = data
        self.setRowCount(len(data))
        
        for row, stock in enumerate(data):
            self.populate_row(row, stock)
        
        # 调整行高
        self.resizeRowsToContents()
    
    def populate_row(self, row, stock):
        """填充行数据"""
        # 排名
        rank_item = QTableWidgetItem(str(stock.get('rank', '')))
        rank_item.setTextAlignment(Qt.AlignCenter)
        self.setItem(row, 0, rank_item)
        
        # 股票代码
        code_item = QTableWidgetItem(stock.get('code', ''))
        code_item.setTextAlignment(Qt.AlignCenter)
        self.setItem(row, 1, code_item)
        
        # 股票名称
        name_item = QTableWidgetItem(stock.get('name', ''))
        name_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.setItem(row, 2, name_item)
        
        # 最新价
        price = stock.get('price', 0)
        price_item = QTableWidgetItem(f"{price:.2f}" if price else "")
        price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.setItem(row, 3, price_item)
        
        # 涨跌幅
        change_pct = stock.get('change_pct', 0)
        change_pct_item = QTableWidgetItem(f"{change_pct:+.2f}%" if change_pct else "")
        change_pct_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        
        # 设置涨跌颜色和背景
        if change_pct > 0:
            change_pct_item.setForeground(QColor(COLORS['rise']))
            change_pct_item.setBackground(QColor(COLORS['rise_bg']))
            # 设置字体加粗
            font = change_pct_item.font()
            font.setBold(True)
            change_pct_item.setFont(font)
        elif change_pct < 0:
            change_pct_item.setForeground(QColor(COLORS['fall']))
            change_pct_item.setBackground(QColor(COLORS['fall_bg']))
            # 设置字体加粗
            font = change_pct_item.font()
            font.setBold(True)
            change_pct_item.setFont(font)
        else:
            change_pct_item.setForeground(QColor(COLORS['equal']))
        
        self.setItem(row, 4, change_pct_item)
        
        # 涨跌额
        change_amount = stock.get('change_amount', 0)
        change_amount_item = QTableWidgetItem(f"{change_amount:+.2f}" if change_amount else "")
        change_amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        
        # 设置涨跌颜色和背景
        if change_amount > 0:
            change_amount_item.setForeground(QColor(COLORS['rise']))
            change_amount_item.setBackground(QColor(COLORS['rise_bg']))
            # 设置字体加粗
            font = change_amount_item.font()
            font.setBold(True)
            change_amount_item.setFont(font)
        elif change_amount < 0:
            change_amount_item.setForeground(QColor(COLORS['fall']))
            change_amount_item.setBackground(QColor(COLORS['fall_bg']))
            # 设置字体加粗
            font = change_amount_item.font()
            font.setBold(True)
            change_amount_item.setFont(font)
        else:
            change_amount_item.setForeground(QColor(COLORS['equal']))
        
        self.setItem(row, 5, change_amount_item)
        
        # 成交量
        volume = stock.get('volume', 0)
        volume_text = self.format_volume(volume)
        volume_item = QTableWidgetItem(volume_text)
        volume_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.setItem(row, 6, volume_item)
        
        # 成交额
        amount = stock.get('amount', 0)
        amount_text = self.format_amount(amount)
        amount_item = QTableWidgetItem(amount_text)
        amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.setItem(row, 7, amount_item)
        
        # 最后一列：人气值或换手率
        if self.table_type == 'popularity':
            popularity = stock.get('popularity', 0)
            last_item = QTableWidgetItem(str(popularity) if popularity else "")
        else:
            turnover = stock.get('turnover', 0)
            last_item = QTableWidgetItem(f"{turnover:.2f}%" if turnover else "")

        last_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.setItem(row, 8, last_item)
    
    def format_volume(self, volume):
        """格式化成交量"""
        if volume == 0:
            return ""
        elif volume >= 100000000:  # 亿手
            return f"{volume/100000000:.1f}亿手"
        elif volume >= 10000:      # 万手
            return f"{volume/10000:.1f}万手"
        else:
            return f"{volume}手"
    
    def format_amount(self, amount):
        """格式化成交额"""
        if amount == 0:
            return ""
        elif amount >= 100000000:  # 亿元
            return f"{amount/100000000:.2f}亿"
        elif amount >= 10000:      # 万元
            return f"{amount/10000:.2f}万"
        else:
            return f"{amount:.0f}"
    
    def clear_data(self):
        """清空数据"""
        self.setRowCount(0)
        self.stock_data = []
    
    def get_selected_stock(self):
        """获取选中的股票"""
        current_row = self.currentRow()
        if current_row >= 0 and current_row < len(self.stock_data):
            return self.stock_data[current_row]
        return None
    
    def search_stock(self, keyword):
        """搜索股票"""
        if not keyword:
            # 显示所有行
            for row in range(self.rowCount()):
                self.setRowHidden(row, False)
            return
        
        keyword = keyword.lower()
        for row in range(self.rowCount()):
            if row < len(self.stock_data):
                stock = self.stock_data[row]
                code = stock.get('code', '').lower()
                name = stock.get('name', '').lower()
                
                # 检查是否匹配
                if keyword in code or keyword in name:
                    self.setRowHidden(row, False)
                else:
                    self.setRowHidden(row, True)
