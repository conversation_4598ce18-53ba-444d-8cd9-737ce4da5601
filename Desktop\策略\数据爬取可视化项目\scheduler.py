"""
数据更新调度器
"""
import schedule
import time
import threading
import logging
from datetime import datetime
from scraper import EastMoneySpider
from database import DatabaseManager
from config import UPDATE_INTERVAL

logger = logging.getLogger(__name__)

class DataScheduler:
    def __init__(self):
        self.spider = EastMoneySpider()
        self.db_manager = DatabaseManager()
        self.is_running = False
        self.scheduler_thread = None
        
    def update_popularity_data(self):
        """更新人气榜数据"""
        try:
            logger.info("开始更新人气榜数据...")
            start_time = datetime.now()
            
            # 获取数据
            data = self.spider.get_popularity_ranking()
            
            if data:
                # 保存到数据库
                success = self.db_manager.save_popularity_data(data)
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                if success:
                    logger.info(f"人气榜数据更新成功，获取{len(data)}条记录，耗时{duration:.2f}秒")
                else:
                    logger.error("人气榜数据保存失败")
            else:
                logger.warning("未获取到人气榜数据")
                
        except Exception as e:
            logger.error(f"更新人气榜数据失败: {e}")
    
    def update_soaring_data(self):
        """更新飙升榜数据"""
        try:
            logger.info("开始更新飙升榜数据...")
            start_time = datetime.now()
            
            # 获取数据
            data = self.spider.get_soaring_ranking()
            
            if data:
                # 保存到数据库
                success = self.db_manager.save_soaring_data(data)
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                if success:
                    logger.info(f"飙升榜数据更新成功，获取{len(data)}条记录，耗时{duration:.2f}秒")
                else:
                    logger.error("飙升榜数据保存失败")
            else:
                logger.warning("未获取到飙升榜数据")
                
        except Exception as e:
            logger.error(f"更新飙升榜数据失败: {e}")
    
    def update_all_data(self):
        """更新所有数据"""
        logger.info("开始全量数据更新...")
        
        # 更新人气榜
        self.update_popularity_data()
        
        # 等待一段时间避免请求过于频繁
        time.sleep(2)
        
        # 更新飙升榜
        self.update_soaring_data()
        
        logger.info("全量数据更新完成")
    
    def setup_schedule(self):
        """设置定时任务"""
        # 每隔指定分钟更新一次数据
        schedule.every(UPDATE_INTERVAL).minutes.do(self.update_all_data)
        
        # 每天早上9点进行一次全量更新
        schedule.every().day.at("09:00").do(self.update_all_data)
        
        # 每天收盘后15:30进行一次全量更新
        schedule.every().day.at("15:30").do(self.update_all_data)
        
        logger.info(f"定时任务设置完成，更新间隔: {UPDATE_INTERVAL}分钟")
    
    def run_scheduler(self):
        """运行调度器"""
        self.setup_schedule()
        
        # 启动时立即执行一次更新
        logger.info("执行初始数据更新...")
        self.update_all_data()
        
        # 开始定时任务循环
        logger.info("开始定时任务循环...")
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                logger.error(f"调度器运行错误: {e}")
                time.sleep(5)
    
    def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已经在运行中")
            return
        
        self.is_running = True
        self.scheduler_thread = threading.Thread(target=self.run_scheduler, daemon=True)
        self.scheduler_thread.start()
        logger.info("数据调度器已启动")
    
    def stop(self):
        """停止调度器"""
        if not self.is_running:
            logger.warning("调度器未在运行")
            return
        
        self.is_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        
        # 清除所有定时任务
        schedule.clear()
        logger.info("数据调度器已停止")
    
    def get_status(self):
        """获取调度器状态"""
        return {
            'is_running': self.is_running,
            'next_run': str(schedule.next_run()) if schedule.jobs else None,
            'jobs_count': len(schedule.jobs),
            'update_interval': UPDATE_INTERVAL
        }

# 全局调度器实例
scheduler_instance = DataScheduler()

def start_scheduler():
    """启动全局调度器"""
    scheduler_instance.start()

def stop_scheduler():
    """停止全局调度器"""
    scheduler_instance.stop()

def get_scheduler_status():
    """获取调度器状态"""
    return scheduler_instance.get_status()

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/scheduler.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    # 启动调度器
    try:
        scheduler = DataScheduler()
        scheduler.start()
        
        # 保持程序运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("接收到停止信号，正在关闭调度器...")
        scheduler.stop()
        logger.info("调度器已关闭")
