"""
配置文件
"""
import os

# 基础配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(BASE_DIR, 'data')
LOG_DIR = os.path.join(BASE_DIR, 'logs')

# 确保目录存在
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(LOG_DIR, exist_ok=True)

# 数据库配置
DATABASE_PATH = os.path.join(DATA_DIR, 'eastmoney_data.db')

# 爬虫配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Referer': 'https://vipmoney.eastmoney.com/',
}

# 东方财富API配置
EASTMONEY_BASE_URL = 'https://vipmoney.eastmoney.com'
POPULARITY_API = 'https://vipmoney.eastmoney.com/api/ranking/popularity'
SOARING_API = 'https://vipmoney.eastmoney.com/api/ranking/soaring'

# 更新频率配置（分钟）
UPDATE_INTERVAL = 5

# Web服务配置
WEB_HOST = '127.0.0.1'
WEB_PORT = 5000
DEBUG = True

# 日志配置
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
