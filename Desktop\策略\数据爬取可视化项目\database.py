"""
数据库操作模块
"""
import sqlite3
import pandas as pd
from datetime import datetime
import logging
from config import DATABASE_PATH

logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self):
        self.db_path = DATABASE_PATH
        self.init_database()
    
    def init_database(self):
        """初始化数据库表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建人气榜表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS popularity_ranking (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        rank_num INTEGER,
                        stock_name TEXT,
                        stock_code TEXT,
                        sector TEXT,
                        current_price REAL,
                        change_percent TEXT,
                        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(stock_code, update_time)
                    )
                ''')
                
                # 创建飙升榜表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS soaring_ranking (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        rank_num INTEGER,
                        stock_name TEXT,
                        stock_code TEXT,
                        sector TEXT,
                        current_price REAL,
                        change_percent TEXT,
                        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(stock_code, update_time)
                    )
                ''')
                
                # 创建数据更新日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS update_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        table_name TEXT,
                        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        records_count INTEGER,
                        status TEXT
                    )
                ''')
                
                conn.commit()
                logger.info("数据库初始化完成")
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def save_popularity_data(self, data_list):
        """保存人气榜数据"""
        return self._save_ranking_data(data_list, 'popularity_ranking')
    
    def save_soaring_data(self, data_list):
        """保存飙升榜数据"""
        return self._save_ranking_data(data_list, 'soaring_ranking')
    
    def _save_ranking_data(self, data_list, table_name):
        """保存排行榜数据的通用方法"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 清除当前时间段的旧数据（保留历史数据）
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M')
                
                for data in data_list:
                    cursor.execute(f'''
                        INSERT OR REPLACE INTO {table_name} 
                        (rank_num, stock_name, stock_code, sector, current_price, change_percent, update_time)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        data['rank_num'],
                        data['stock_name'],
                        data['stock_code'],
                        data['sector'],
                        data['current_price'],
                        data['change_percent'],
                        current_time
                    ))
                
                # 记录更新日志
                cursor.execute('''
                    INSERT INTO update_log (table_name, records_count, status)
                    VALUES (?, ?, ?)
                ''', (table_name, len(data_list), 'SUCCESS'))
                
                conn.commit()
                logger.info(f"成功保存{len(data_list)}条{table_name}数据")
                return True
                
        except Exception as e:
            logger.error(f"保存{table_name}数据失败: {e}")
            return False
    
    def get_latest_popularity_data(self, limit=100):
        """获取最新的人气榜数据"""
        return self._get_latest_ranking_data('popularity_ranking', limit)
    
    def get_latest_soaring_data(self, limit=100):
        """获取最新的飙升榜数据"""
        return self._get_latest_ranking_data('soaring_ranking', limit)
    
    def _get_latest_ranking_data(self, table_name, limit):
        """获取最新排行榜数据的通用方法"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = f'''
                    SELECT rank_num, stock_name, stock_code, sector, 
                           current_price, change_percent, update_time
                    FROM {table_name}
                    WHERE update_time = (
                        SELECT MAX(update_time) FROM {table_name}
                    )
                    ORDER BY rank_num
                    LIMIT ?
                '''
                df = pd.read_sql_query(query, conn, params=[limit])
                return df.to_dict('records')
                
        except Exception as e:
            logger.error(f"获取{table_name}数据失败: {e}")
            return []
    
    def get_update_status(self):
        """获取数据更新状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = '''
                    SELECT table_name, MAX(update_time) as last_update, 
                           records_count, status
                    FROM update_log
                    GROUP BY table_name
                    ORDER BY last_update DESC
                '''
                df = pd.read_sql_query(query, conn)
                return df.to_dict('records')
                
        except Exception as e:
            logger.error(f"获取更新状态失败: {e}")
            return []
