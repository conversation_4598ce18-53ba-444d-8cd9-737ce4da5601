"""
配置文件
"""
import os

# 基础配置
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DATA_DIR = os.path.join(BASE_DIR, 'data')

# 确保目录存在
os.makedirs(DATA_DIR, exist_ok=True)

# 数据库配置
DATABASE_PATH = os.path.join(DATA_DIR, 'eastmoney_stocks.db')

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# 东方财富URL配置
EASTMONEY_URLS = {
    # 人气榜API
    'popularity': 'https://push2.eastmoney.com/api/qt/clist/get?cb=jQuery&pn=1&pz=100&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f164&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f164',
    
    # 飙升榜API  
    'soaring': 'https://push2.eastmoney.com/api/qt/clist/get?cb=jQuery&pn=1&pz=100&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f3&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25',
    
    # 备用URL
    'backup_popularity': 'https://datacenter-web.eastmoney.com/api/data/v1/get?sortColumns=POPULARITY&sortTypes=-1&pageSize=100&pageNumber=1&reportName=RPT_POPULARITYRANK_STOCK&columns=ALL',
    
    'backup_soaring': 'https://datacenter-web.eastmoney.com/api/data/v1/get?sortColumns=CHANGE_RATE&sortTypes=-1&pageSize=100&pageNumber=1&reportName=RPT_LICO_FU_BS&columns=ALL'
}

# 更新配置
UPDATE_INTERVAL = 5 * 60 * 1000  # 5分钟，单位毫秒
RETRY_COUNT = 3
RETRY_DELAY = 2  # 秒

# 界面配置
WINDOW_TITLE = "东方财富实时榜单监控"
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
MIN_WINDOW_WIDTH = 800
MIN_WINDOW_HEIGHT = 600

# 表格配置
TABLE_COLUMNS = {
    'popularity': [
        ('排名', 60),
        ('股票代码', 80),
        ('股票名称', 120),
        ('最新价', 80),
        ('涨跌幅', 80),
        ('涨跌额', 80),
        ('成交量', 100),
        ('成交额', 100),
        ('人气值', 80)
    ],
    'soaring': [
        ('排名', 60),
        ('股票代码', 80),
        ('股票名称', 120),
        ('最新价', 80),
        ('涨跌幅', 80),
        ('涨跌额', 80),
        ('成交量', 100),
        ('成交额', 100),
        ('换手率', 80)
    ]
}

# 颜色配置
COLORS = {
    'rise': '#e53e3e',      # 上涨红色 (更现代的红色)
    'fall': '#38a169',      # 下跌绿色 (更现代的绿色)
    'equal': '#718096',     # 平盘灰色
    'background': '#ffffff', # 背景白色
    'text': '#2d3748',      # 文字颜色
    'rise_bg': '#fed7d7',   # 上涨背景色
    'fall_bg': '#c6f6d5',   # 下跌背景色
    'border': '#e2e8f0'     # 边框颜色
}
