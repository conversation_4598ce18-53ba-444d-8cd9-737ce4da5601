"""
东方财富实时榜单监控 - PyQt5版本
主程序入口
"""
import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon, QFont

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.main_window import MainWindow

def main():
    """主函数"""
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("东方财富实时榜单监控")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("股票监控工具")
    
    # 设置高DPI支持
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # 设置默认字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 设置现代化样式
    app.setStyleSheet("""
        QMainWindow {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
        }

        QWidget {
            font-family: 'Microsoft YaHei UI', 'Segoe UI', Arial;
        }

        /* 工具栏按钮样式 */
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #4285f4, stop:1 #3367d6);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 13px;
            min-width: 100px;
        }
        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #5a95f5, stop:1 #4285f4);
            transform: translateY(-1px);
        }
        QPushButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3367d6, stop:1 #2c5aa0);
        }
        QPushButton:disabled {
            background: #bdc3c7;
            color: #7f8c8d;
        }

        /* 搜索框样式 */
        QLineEdit {
            background-color: white;
            border: 2px solid #e1e8ed;
            border-radius: 25px;
            padding: 12px 20px;
            font-size: 14px;
            color: #2c3e50;
        }
        QLineEdit:focus {
            border-color: #4285f4;
            outline: none;
        }
        QLineEdit::placeholder {
            color: #95a5a6;
        }

        /* 分组框样式 */
        QGroupBox {
            background: white;
            border: none;
            border-radius: 12px;
            margin-top: 20px;
            padding-top: 20px;
            font-weight: 700;
            font-size: 16px;
            color: #2c3e50;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 20px;
            padding: 8px 16px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
            color: white;
            border-radius: 20px;
            font-size: 14px;
        }

        /* 表格样式 */
        QTableWidget {
            background-color: white;
            border: none;
            border-radius: 8px;
            gridline-color: #f1f3f4;
            selection-background-color: #e8f0fe;
            alternate-background-color: #fafbfc;
        }
        QTableWidget::item {
            padding: 12px 8px;
            border: none;
            border-bottom: 1px solid #f1f3f4;
        }
        QTableWidget::item:selected {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #e8f0fe, stop:1 #d2e3fc);
            color: #1a73e8;
        }
        QTableWidget::item:hover {
            background-color: #f8f9fa;
        }

        /* 表头样式 */
        QHeaderView::section {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
            color: #5f6368;
            padding: 12px 8px;
            border: none;
            border-bottom: 2px solid #4285f4;
            font-weight: 600;
            font-size: 13px;
        }
        QHeaderView::section:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #e8f0fe, stop:1 #d2e3fc);
        }

        /* 状态栏样式 */
        QStatusBar {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8f9fa);
            border-top: 1px solid #e1e8ed;
            color: #5f6368;
            font-size: 12px;
        }

        /* 进度条样式 */
        QProgressBar {
            border: none;
            border-radius: 8px;
            background-color: #e1e8ed;
            text-align: center;
            color: white;
            font-weight: 600;
        }
        QProgressBar::chunk {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #4285f4, stop:1 #34a853);
            border-radius: 8px;
        }

        /* 标签样式 */
        QLabel {
            color: #5f6368;
            font-size: 13px;
        }

        /* 分割线样式 */
        QFrame[frameShape="4"] {
            color: #e1e8ed;
        }
    """)
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
